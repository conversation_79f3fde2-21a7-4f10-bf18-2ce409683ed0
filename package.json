{"name": "sergio-react", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "sass": "node-sass --watch public/static/scss/style.scss public/assets/css/style.css --source-map public/static/css/style.css.map", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.0.1", "isotope-layout": "^3.0.6", "next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-tsparticles": "^2.12.0", "sass": "^1.69.0", "swiper": "^11.0.3", "tsparticles": "^2.12.0", "typed.js": "^2.1.0", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "14.1.0"}}