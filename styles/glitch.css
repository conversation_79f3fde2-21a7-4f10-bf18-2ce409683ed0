.glitch {
  background: url("/img/hero/1.jpg") no-repeat 50% 50% / cover;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  position: absolute;
}
.glitch:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  animation: bgGlitch 1.5s both infinite alternate;
  opacity: 0.6;
}
@keyframes bgGlitch {
  0%,
  100% {
    left: 0;
    clip-path: none;
  }
  13% {
    left: 5.5px;
    clip-path: inset(21% 0 4% 0);
  }
  18%,
  8% {
    left: 0;
    clip-path: none;
  }
  33% {
    left: -1.5px;
    clip-path: inset(3% 0 4% 0);
  }
  38%,
  28% {
    left: 0;
    clip-path: none;
  }
  51% {
    left: -0.5px;
    clip-path: inset(2% 0 7% 0);
  }
  56%,
  46% {
    left: 0;
    clip-path: none;
  }
  71% {
    left: 7.5px;
    clip-path: inset(2% 0 6% 0);
  }
  76%,
  66% {
    left: 0;
    clip-path: none;
  }
  93% {
    left: 7.5px;
    clip-path: inset(29% 0 13% 0);
  }
  98%,
  88% {
    left: 0;
    clip-path: none;
  }
}
