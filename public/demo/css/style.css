
.section-title h2.dark-color {
	color: #000 !important;
}

.product a {
	text-decoration: none;
	color: #fff
}

.page-section {
	padding: 80px 0;
	background: #f3f3f3
}

.page-section-01 {
	padding: 80px 0;
	background: #fff
}

.product {
	margin: 0 auto 40px;
	max-width: 100%;
	overflow: hidden;
	background: #fff;
	box-shadow: 0 8px 50px -6px rgba(84, 84, 120, .26)
}

.product .product-img {
	position: relative
}

.product .preview-link {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	opacity: 0;
	transition: ease all 0.3s
}

.product:hover .preview-link {
	opacity: 1
}

.product .count {
	position: absolute;
	top: -20px;
	right: -30px;
	background: #fe1c5b;
	width: 50px;
	height: 50px;
	text-align: center;
	line-height: 50px;
	color: #fff;
	font-weight: 600;
	border-radius: 50%;
	box-shadow: 0 0 32px rgba(0, 0, 0, 0.25);
	z-index: 1
}

.product .count.paid {
	background: #8343ff
}

.product .product-info {
	padding: 0;
	width: 100%;
	display: inline-block;
	vertical-align: top
}

.product h2 {
	margin: 0;
	font-size: 18px;
	font-weight: 500;
	text-align: center;
	padding: 28px 20px;
	color: #111;
	transition: ease all 0.3s;
	border-top: 1px solid #ddd;
	float: left;
	width: 100%
}

.product:hover h2 {
}

.product .product-btns .btn-01 {
	margin-right: 15px
}

.product .product-btns .btn:hover, .product .product-btns .btn:active, .product .product-btns .btn:focus {
	color: #fff
}

footer {
	background: #111;
	text-align: center;
	padding: 30px 0
}

footer p {
	margin: 0;
	color: #fff;
	opacity: 0.8;
	font-size: 13px
}

.purchase-section {
	text-align: center;
	padding: 80px 0
}

.purchase-section h3 {
	width: 700px;
	margin: 0 auto;
	font-size: 35px;
	display: inline-block;
	padding: 10px 0 30px;
	letter-spacing: 1px;
	line-height: 45px;
}

.purchase-section a i {
	margin-right: 8px
}