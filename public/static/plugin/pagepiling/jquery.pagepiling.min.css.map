{"version": 3, "sources": ["jquery.pagepiling.css"], "names": [], "mappings": ";;;;;;;AAQM,KAAN,KACI,SAAS,OACT,OAAO,EACP,QAAQ,EAGR,4BAAgG,cAEpG,YACI,OAAO,KACP,SAAS,SACT,MAAM,KAGN,oBAAyH,OAE7H,WACI,mBAAoB,IAAI,GAAO,6BAC/B,gBAAiB,IAAI,GAAO,6BAC5B,cAAe,IAAI,GAAO,6BAC1B,WAAY,IAAI,GAAO,6BAEvB,mCAAqJ,6BACrJ,gCAAiC,6BACjC,8BAA+B,6BAC/B,2BAA4B,6BAGhC,QACI,SAAU,MACV,QAAS,IACT,WAAY,MACZ,IAAK,IACL,QAAS,EAEb,cACI,MAAO,KAEX,aACI,KAAM,KAEV,qBACI,QAAS,MAEb,cACI,QAAS,WACT,eAAgB,OAChB,MAAO,KACP,OAAQ,KAEZ,cACI,SAAU,SACV,QAAS,EACT,KAAM,IACN,QAAS,EAEb,qBACI,OAAQ,KAEZ,kBACI,IAAK,KAET,WACA,iBACE,OAAQ,EACR,QAAS,EAEX,WACA,iBACI,QAAS,MACT,MAAO,KACP,OAAQ,KACR,OAAQ,IACR,SAAS,SAEb,iBACI,QAAS,aAEb,aACA,mBACI,QAAS,MACT,SAAU,SACV,QAAS,EACT,MAAO,KACP,OAAQ,KACR,OAAQ,QACR,gBAAiB,KAErB,wBACA,2BACI,WAAY,KAEhB,aACA,mBACI,IAAK,IACL,KAAM,IACN,MAAO,IACP,OAAQ,IACR,OAAQ,IAAI,MAAM,KAClB,WAAY,cACZ,cAAe,IACf,SAAU,SACV,QAAS,EAEb,YACI,SAAU,SACV,IAAK,KACL,MAAO,KACP,UAAW,KACX,YAAa,MAAO,UAAW,WAC/B,YAAa,OACb,UAAW,MAEf,kBACI,MAAO,KAEX,iBACI,KAAM,KAEV,eACI,WAAY,OACZ,OAAQ", "file": "jquery.pagepiling.min.css", "sourcesContent": ["/*!\r\n * pagepiling.js 1.5.6\r\n *\r\n * https://github.com/alvarotrigo/pagePiling.js\r\n * @license MIT licensed\r\n *\r\n * Copyright (C) 2016 alvarotrigo.com - A project by <PERSON><PERSON>\r\n */\r\nhtml, body {\r\n    overflow:hidden;\r\n    margin:0;\r\n    padding:0;\r\n\r\n    /*Avoid flicker on slides transitions for mobile phones #336 */\r\n    -webkit-tap-highlight-color: rgba(0,0,0,0);\r\n}\r\n.pp-section {\r\n    height:100%;\r\n    position:absolute;\r\n    width:100%;\r\n    \r\n    /* fixes flickering in firefox*/\r\n    backface-visibility: hidden; \r\n}\r\n.pp-easing {\r\n    -webkit-transition: all 1000ms cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    -moz-transition: all 1000ms cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    -o-transition: all 1000ms cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    transition: all 1000ms cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    /* custom */\r\n    -webkit-transition-timing-function: cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    -moz-transition-timing-function: cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    -o-transition-timing-function: cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    transition-timing-function: cubic-bezier(0.550, 0.085, 0.000, 0.990);\r\n    /* custom */\r\n}\r\n#pp-nav {\r\n    position: fixed;\r\n    z-index: 100;\r\n    margin-top: -32px;\r\n    top: 50%;\r\n    opacity: 1;\r\n}\r\n#pp-nav.right {\r\n    right: 17px;\r\n}\r\n#pp-nav.left {\r\n    left: 17px;\r\n}\r\n.pp-section.pp-table{\r\n    display: table;\r\n}\r\n.pp-tableCell {\r\n    display: table-cell;\r\n    vertical-align: middle;\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n.pp-slidesNav{\r\n    position: absolute;\r\n    z-index: 4;\r\n    left: 50%;\r\n    opacity: 1;\r\n}\r\n.pp-slidesNav.bottom {\r\n    bottom: 17px;\r\n}\r\n.pp-slidesNav.top {\r\n    top: 17px;\r\n}\r\n#pp-nav ul,\r\n.pp-slidesNav ul {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n#pp-nav li,\r\n.pp-slidesNav li {\r\n    display: block;\r\n    width: 14px;\r\n    height: 13px;\r\n    margin: 7px;\r\n    position:relative;\r\n}\r\n.pp-slidesNav li {\r\n    display: inline-block;\r\n}\r\n#pp-nav li a,\r\n.pp-slidesNav li a {\r\n    display: block;\r\n    position: relative;\r\n    z-index: 1;\r\n    width: 100%;\r\n    height: 100%;\r\n    cursor: pointer;\r\n    text-decoration: none;\r\n}\r\n#pp-nav li .active span,\r\n.pp-slidesNav .active span {\r\n    background: #333;\r\n}\r\n#pp-nav span,\r\n.pp-slidesNav span {\r\n    top: 2px;\r\n    left: 2px;\r\n    width: 8px;\r\n    height: 8px;\r\n    border: 1px solid #000;\r\n    background: rgba(0, 0, 0, 0);\r\n    border-radius: 50%;\r\n    position: absolute;\r\n    z-index: 1;\r\n}\r\n.pp-tooltip {\r\n    position: absolute;\r\n    top: -2px;\r\n    color: #fff;\r\n    font-size: 14px;\r\n    font-family: arial, helvetica, sans-serif;\r\n    white-space: nowrap;\r\n    max-width: 220px;\r\n}\r\n.pp-tooltip.right {\r\n    right: 20px;\r\n}\r\n.pp-tooltip.left {\r\n    left: 20px;\r\n}\r\n.pp-scrollable{\r\n    overflow-y: scroll;\r\n    height: 100%;\r\n}\r\n"]}